{"$schema": "https://biomejs.dev/schemas/2.2.2/schema.json", "files": {"includes": ["src/features/**", "src/components/layout/**"]}, "formatter": {"enabled": true, "indentStyle": "space", "indentWidth": 2, "lineWidth": 120, "bracketSpacing": true, "attributePosition": "auto"}, "javascript": {"formatter": {"quoteStyle": "double", "jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "all", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false}}, "linter": {"enabled": true, "rules": {"recommended": true}}, "assist": {"enabled": true, "actions": {"source": {"organizeImports": {"level": "on"}}}}}