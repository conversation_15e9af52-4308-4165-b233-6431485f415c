/**
 * Project Components
 *
 * All React components for the projects feature.
 * Organized by sub-feature:
 *
 * - ProjectDashboard: Main project view orchestrator
 * - ProjectManagement: Project CRUD, selection, metadata
 * - TaskManagement: Task CRUD, status management
 * - TaskBoard: Kanban board with drag-drop
 * - TaskTable: Table view with filters/sorting
 * - DocumentManagement: Project documents and editing
 * - VersionHistory: Document versioning
 */

export { NewProjectModal } from "./NewProjectModal";
export { ProjectCard } from "./ProjectCard";
export { ProjectCardActions } from "./ProjectCardActions";
export { ProjectHeader } from "./ProjectHeader";
export { ProjectList } from "./ProjectList";
