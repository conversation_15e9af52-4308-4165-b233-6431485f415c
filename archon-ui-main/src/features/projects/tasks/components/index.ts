/**
 * Task Management Components
 *
 * Simplified and refactored task components following vertical slice architecture.
 * Removed complex flip animations and over-engineering for better maintainability.
 */

export { EditableTableCell } from "./EditableTableCell";
export { FeatureSelect } from "./FeatureSelect";
export { KanbanColumn } from "./KanbanColumn";
export { TaskAssignee } from "./TaskAssignee";
export type { TaskCardProps } from "./TaskCard";
export { TaskCard } from "./TaskCard";
export { TaskCardActions } from "./TaskCardActions";
export { TaskEditModal } from "./TaskEditModal";
export { TaskPriority as TaskPriorityComponent } from "./TaskPriority";
