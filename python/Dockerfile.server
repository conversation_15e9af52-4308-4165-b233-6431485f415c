# Server Service - Web crawling and document processing microservice
FROM python:3.12 AS builder

WORKDIR /build

# Install build dependencies and uv
RUN apt-get update && apt-get install -y \
    build-essential \
    && rm -rf /var/lib/apt/lists/* \
    && pip install --no-cache-dir uv

# Copy pyproject.toml for dependency installation
COPY pyproject.toml .

# Install server dependencies to a virtual environment using uv
RUN uv venv /venv && \
    . /venv/bin/activate && \
    uv pip install --group server --group server-reranking

# Runtime stage
FROM python:3.12-slim

WORKDIR /app

# Install runtime dependencies for Playwright (minimal set)
RUN apt-get update && apt-get install -y \
    wget \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libatk1.0-0 \
    libatspi2.0-0 \
    libcups2 \
    libdbus-1-3 \
    libdrm2 \
    libgbm1 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libwayland-client0 \
    libxcomposite1 \
    libxdamage1 \
    libxfixes3 \
    libxkbcommon0 \
    libxrandr2 \
    xdg-utils \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Copy the virtual environment from builder
COPY --from=builder /venv /venv

# Install Playwright browsers
ENV PATH=/venv/bin:$PATH
RUN playwright install chromium

# Copy server code and tests
COPY src/server/ src/server/
COPY src/__init__.py src/
COPY tests/ tests/

# Set environment variables
ENV PYTHONPATH="/app:$PYTHONPATH"
ENV PYTHONUNBUFFERED=1
ENV PATH="/venv/bin:$PATH"

# Expose Server port
ARG ARCHON_SERVER_PORT=8181
ENV ARCHON_SERVER_PORT=${ARCHON_SERVER_PORT}
EXPOSE ${ARCHON_SERVER_PORT}

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD sh -c "python -c \"import urllib.request; urllib.request.urlopen('http://localhost:${ARCHON_SERVER_PORT}/health')\""

# Run the Server service
CMD sh -c "python -m uvicorn src.server.main:socket_app --host 0.0.0.0 --port ${ARCHON_SERVER_PORT} --workers 1"