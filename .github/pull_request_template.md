# Pull Request

## Summary
<!-- Provide a brief description of what this PR accomplishes -->

## Changes Made
<!-- List the main changes in this PR -->
- 
- 
- 

## Type of Change
<!-- Mark the relevant option with an "x" -->
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update
- [ ] Performance improvement
- [ ] Code refactoring

## Affected Services
<!-- Mark all that apply with an "x" -->
- [ ] Frontend (React UI)
- [ ] Server (FastAPI backend)
- [ ] MCP Server (Model Context Protocol)
- [ ] Agents (PydanticAI service)
- [ ] Database (migrations/schema)
- [ ] Docker/Infrastructure
- [ ] Documentation site

## Testing
<!-- Describe how you tested your changes -->
- [ ] All existing tests pass
- [ ] Added new tests for new functionality
- [ ] Manually tested affected user flows
- [ ] Docker builds succeed for all services

### Test Evidence
<!-- Provide specific test commands run and their results -->
```bash
# Example: python -m pytest tests/
# Example: cd archon-ui-main && npm run test
```

## Checklist
<!-- Mark completed items with an "x" -->
- [ ] My code follows the service architecture patterns
- [ ] If using an AI coding assistant, I used the CLAUDE.md rules
- [ ] I have added tests that prove my fix/feature works
- [ ] All new and existing tests pass locally
- [ ] My changes generate no new warnings
- [ ] I have updated relevant documentation
- [ ] I have verified no regressions in existing features

## Breaking Changes
<!-- If this PR introduces breaking changes, describe them here -->
<!-- Include migration steps if applicable -->

## Additional Notes
<!-- Any additional information that reviewers should know -->
<!-- Screenshots, performance metrics, dependencies added, etc. -->