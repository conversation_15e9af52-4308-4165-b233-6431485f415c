/**
 * Archon Documentation - Aurora Borealis Glass Theme
 * Using colors from the Archon neon logo SVG
 */

/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* Aurora borealis color palette from logo SVG */
:root {
  /* Primary aurora colors from logo gradient */
  --aurora-green: #00d38a;
  --aurora-teal: #0fcaa6;
  --aurora-blue: #36b5ef;
  --aurora-cyan: #3fb1ff;
  --aurora-pink: #fe6aff;
  --aurora-purple: #d964ff;
  --aurora-violet: #ab5dff;
  --aurora-indigo: #8a59ff;
  --aurora-deep-purple: #7656ff;
  --aurora-core: #6f55ff;
  --aurora-magenta: #9a3df8;
  --aurora-hot-pink: #c624f2;
  --aurora-electric: #e214ee;
  --aurora-neon: #ed0fed;

  /* Primary theme colors using aurora palette */
  --ifm-color-primary: var(--aurora-core);
  --ifm-color-primary-dark: var(--aurora-indigo);
  --ifm-color-primary-darker: var(--aurora-violet);
  --ifm-color-primary-darkest: var(--aurora-purple);
  --ifm-color-primary-light: var(--aurora-cyan);
  --ifm-color-primary-lighter: var(--aurora-blue);
  --ifm-color-primary-lightest: var(--aurora-teal);

  /* Background and surface colors */
  --ifm-background-color: #0a0a0a;
  --ifm-background-surface-color: #111111;
  --ifm-color-content: #ffffff;
  --ifm-color-content-secondary: #a1a1aa;
  
  /* Typography */
  --ifm-font-color-base: #ffffff;
  --ifm-font-color-secondary: #e5e7eb;
  --ifm-heading-color: #ffffff;
  
  /* Links with aurora gradient */
  --ifm-link-color: var(--aurora-cyan);
  --ifm-link-hover-color: var(--aurora-pink);
  
  /* Cards and surfaces */
  --ifm-card-background-color: rgba(17, 17, 17, 0.8);
  
  /* Navbar */
  --ifm-navbar-background-color: #000000;
  --ifm-navbar-link-color: #ffffff;
  --ifm-navbar-link-hover-color: var(--aurora-cyan);
  
  /* Sidebar - smaller width like TOC */
  --ifm-sidebar-background-color: rgba(10, 10, 10, 0.8);
  --ifm-sidebar-width: 240px; /* Much smaller like TOC */
  
  /* Footer */
  --ifm-footer-background-color: #000000;
  --ifm-footer-color: #e5e7eb;
  --ifm-footer-link-color: #a1a1aa;
  --ifm-footer-link-hover-color: var(--aurora-cyan);
  --ifm-footer-title-color: #ffffff;
  
  /* Code blocks */
  --ifm-code-background: #1a1a1a;
  --prism-background-color: #1a1a1a;
  --ifm-code-font-size: 95%;
  
  /* Borders */
  --ifm-border-color: #333333;
  --ifm-toc-border-color: #333333;
}

/* Force dark theme */
html[data-theme='dark'] {
  /* Override any light mode settings */
}

/* Fixed grid background for parallax effect with aurora colors */
html {
  background: 
    radial-gradient(circle at 25% 25%, rgba(0, 211, 138, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(54, 181, 239, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(237, 15, 237, 0.05) 0%, transparent 70%),
    linear-gradient(to right, rgba(111, 85, 255, 0.1) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(111, 85, 255, 0.1) 1px, transparent 1px),
    #000000;
  background-size: 100% 100%, 100% 100%, 100% 100%, 60px 60px, 60px 60px;
  background-attachment: fixed;
  min-height: 100vh;
}

body {
  background: transparent;
  color: #ffffff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Smaller sidebar with glass morphism */
.theme-doc-sidebar-container {
  background: rgba(10, 10, 10, 0.8) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border-right: 1px solid rgba(111, 85, 255, 0.2) !important;
}

.theme-doc-sidebar-menu {
  font-size: 0.875rem; /* Smaller font like TOC */
  padding: 0.5rem;
  background: transparent;
}

.theme-doc-sidebar-item-link {
  font-size: 0.875rem;
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  margin-bottom: 2px;
}

.theme-doc-sidebar-item-link:hover {
  background: rgba(54, 181, 239, 0.1);
  color: var(--aurora-cyan);
  backdrop-filter: blur(10px);
}

.theme-doc-sidebar-item-link--active {
  background: linear-gradient(135deg, 
    rgba(0, 211, 138, 0.15), 
    rgba(54, 181, 239, 0.15), 
    rgba(111, 85, 255, 0.15)
  );
  border-left: 3px solid var(--aurora-cyan);
  color: var(--aurora-cyan);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 8px rgba(54, 181, 239, 0.1);
}

/* Clean navbar styling */
.navbar {
  border-bottom: 1px solid #333333;
}

/* ====== HERO SECTION WITH AURORA GLASS EFFECT ====== */
.hero {
  background: 
    radial-gradient(ellipse at top left, rgba(0, 211, 138, 0.3) 0%, transparent 70%),
    radial-gradient(ellipse at top right, rgba(54, 181, 239, 0.2) 0%, transparent 70%),
    radial-gradient(ellipse at bottom, rgba(237, 15, 237, 0.2) 0%, transparent 70%),
    linear-gradient(135deg, #000000 0%, #0a0a0a 100%) !important;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(111, 85, 255, 0.3);
}

[data-theme='dark'] .hero {
  background: 
    radial-gradient(ellipse at top left, rgba(0, 211, 138, 0.4) 0%, transparent 70%),
    radial-gradient(ellipse at top right, rgba(54, 181, 239, 0.3) 0%, transparent 70%),
    radial-gradient(ellipse at bottom, rgba(237, 15, 237, 0.3) 0%, transparent 70%),
    linear-gradient(135deg, #000000 0%, #0a0a0a 100%) !important;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent 30%, 
    rgba(0, 211, 138, 0.1) 40%,
    rgba(54, 181, 239, 0.1) 50%, 
    rgba(237, 15, 237, 0.1) 60%,
    transparent 70%
  );
  animation: shimmer 4s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: translateX(-150%); opacity: 0; }
  50% { transform: translateX(150%); opacity: 1; }
}

.hero__title {
  color: #ffffff !important;
  text-shadow: 
    0 0 40px var(--aurora-cyan), 
    0 0 80px var(--aurora-blue),
    0 0 120px var(--aurora-green);
  font-weight: 700;
  letter-spacing: -0.025em;
  font-size: 3rem;
}

.hero__subtitle {
  color: rgba(255, 255, 255, 0.95) !important;
  text-shadow: 0 0 30px var(--aurora-cyan);
  font-size: 1.25rem;
  font-weight: 400;
}

/* Aurora neon glass button for Get Started */
.button--green-neon {
  background: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(16px);
  border: 1px solid var(--aurora-green) !important;
  color: var(--aurora-green) !important;
  box-shadow: 0 8px 32px rgba(0, 211, 138, 0.3);
  font-weight: 600;
  text-shadow: 0 0 10px var(--aurora-green);
  border-radius: 12px;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.button--green-neon::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(0, 211, 138, 0.2), 
    rgba(54, 181, 239, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.button--green-neon:hover {
  background: rgba(0, 0, 0, 0.8) !important;
  border-color: var(--aurora-cyan) !important;
  transform: translateY(-3px);
  box-shadow: 0 15px 50px rgba(54, 181, 239, 0.5);
  color: var(--aurora-cyan) !important;
  text-shadow: 0 0 15px var(--aurora-cyan);
  text-decoration: none;
}

.button--green-neon:hover::before {
  left: 100%;
}

/* Clean footer - aurora glass effect */
.footer {
  background: rgba(0, 0, 0, 0.95) !important;
  border-top: 1px solid #333333 !important;
  backdrop-filter: blur(10px);
}

.footer__title {
  color: #ffffff !important;
  font-weight: 600 !important;
  margin-bottom: 0.75rem !important;
}

.footer__link-item {
  color: rgba(255, 255, 255, 0.7) !important;
  transition: color 0.2s ease !important;
}

.footer__link-item:hover {
  color: var(--aurora-cyan) !important;
}

/* Code block improvements */
.prism-code {
  background: #1a1a1a !important;
  border: 1px solid #333333;
  border-radius: 8px;
}

/* Alert/admonition styling */
.alert {
  border-radius: 8px;
  border: 1px solid #333333;
}

[data-theme='dark'] .alert {
  background: rgba(17, 17, 17, 0.8);
}

.alert--info {
  border-left: 4px solid #3b82f6;
}

.alert--warning {
  border-left: 4px solid #f59e0b;
}

.alert--danger {
  border-left: 4px solid #ef4444;
}

.alert--success {
  border-left: 4px solid #10b981;
}

/* Clean card styling */
.card {
  background: var(--ifm-card-background-color);
  border: 1px solid #333333;
  border-radius: 8px;
}

/* ====== INTRO PAGE FEATURE CARDS WITH AURORA GLASS CONTAINERS ====== */
/* Only apply to intro page feature cards, not other pages */
.theme-doc-markdown .row .col > div,
.markdown .row .col > div {
  height: 100%;
  padding: 0.75rem;
  border-radius: 8px; /* Subtle 8px corners, not pills */
  transition: all 0.3s ease;
  position: relative;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(0, 0, 0, 0.7);
  border: 1px solid rgba(111, 85, 255, 0.4);
  box-shadow: 0 10px 30px -10px rgba(0, 0, 0, 0.9);
  min-height: 120px;
}

.theme-doc-markdown .row .col > div::before,
.markdown .row .col > div::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    var(--aurora-green), 
    var(--aurora-cyan), 
    var(--aurora-pink), 
    var(--aurora-purple)
  );
  box-shadow: 0 0 20px 5px rgba(111, 85, 255, 0.8);
  border-radius: 8px 8px 0 0;
}

.theme-doc-markdown .row .col > div::after,
.markdown .row .col > div::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  background: linear-gradient(to bottom, 
    rgba(111, 85, 255, 0.15), 
    rgba(111, 85, 255, 0.03)
  );
  border-radius: 8px 8px 0 0;
  pointer-events: none;
  z-index: 0;
}

.theme-doc-markdown .row .col > div > *,
.markdown .row .col > div > * {
  position: relative;
  z-index: 1;
}

.theme-doc-markdown .row .col > div:hover,
.markdown .row .col > div:hover {
  transform: translateY(-6px);
  box-shadow: 0 25px 60px rgba(54, 181, 239, 0.4);
  border-color: rgba(54, 181, 239, 0.6);
}

/* Feature titles and content with enhanced visibility - intro page only */
.theme-doc-markdown .row .col h3,
.markdown .row .col h3 {
  color: #ffffff;
  text-shadow: 0 0 10px var(--aurora-cyan);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  z-index: 10;
  position: relative;
}

.theme-doc-markdown .row .col p,
.markdown .row .col p {
  color: #f3f4f6;
  line-height: 1.4;
  font-size: 0.8rem;
  z-index: 10;
  position: relative;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

/* Enhanced SVGs with aurora glow */
.featureSvg {
  height: 40px;
  width: 40px;
  filter: drop-shadow(0 0 10px var(--aurora-cyan));
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.featureSvg:hover {
  filter: drop-shadow(0 0 25px var(--aurora-pink));
  transform: scale(1.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .hero__title {
    font-size: 2.5rem;
  }
  
  .hero__subtitle {
    font-size: 1.1rem;
  }

  .theme-doc-markdown .row .col > div,
  .markdown .row .col > div {
    padding: 0.625rem;
    min-height: 110px;
  }
  
  .theme-doc-markdown .row .col h3,
  .markdown .row .col h3 {
    font-size: 0.95rem;
    margin-bottom: 0.4rem;
  }
  
  .theme-doc-markdown .row .col p,
  .markdown .row .col p {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .featureSvg {
    height: 35px;
    width: 35px;
  }
}

@media (max-width: 480px) {
  .hero__title {
    font-size: 2rem;
  }
  
  .hero__subtitle {
    font-size: 1rem;
  }

  .theme-doc-markdown .row .col > div,
  .markdown .row .col > div {
    padding: 0.5rem;
    min-height: 100px;
  }
  
  .theme-doc-markdown .row .col h3,
  .markdown .row .col h3 {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
  }
  
  .theme-doc-markdown .row .col p,
  .markdown .row .col p {
    font-size: 0.7rem;
    line-height: 1.2;
  }

  .featureSvg {
    height: 30px;
    width: 30px;
    margin-bottom: 0.4rem;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a855f7;
}

/* Selection styling */
::selection {
  background: rgba(168, 85, 247, 0.3);
  color: #ffffff;
}

::-moz-selection {
  background: rgba(168, 85, 247, 0.3);
  color: #ffffff;
}

/* ====== HOMEPAGE GLASS CONTAINERS ====== */
/* Specific styling for homepage index.js glass containers */
.glassContainer {
  height: 100%;
  padding: 1.5rem;
  border-radius: 16px;
  transition: all 0.3s ease;
  position: relative;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(168, 85, 247, 0.3);
  box-shadow: 0 15px 35px -10px rgba(0, 0, 0, 0.8);
  margin-bottom: 2rem;
}

.glassContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #a855f7, #3b82f6, #ec4899, #10b981);
  box-shadow: 0 0 30px 8px rgba(168, 85, 247, 0.6);
  border-radius: 16px 16px 0 0;
}

.glassContainer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background: linear-gradient(to bottom, rgba(168, 85, 247, 0.2), rgba(168, 85, 247, 0.05));
  border-radius: 16px 16px 0 0;
  pointer-events: none;
  z-index: 0;
}

.glassContainer > * {
  position: relative;
  z-index: 1;
}

.glassContainer:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 70px rgba(168, 85, 247, 0.3);
  border-color: rgba(168, 85, 247, 0.5);
}

.glassContainer h3 {
  color: #ffffff;
  text-shadow: 0 0 15px rgba(168, 85, 247, 0.7);
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  z-index: 10;
  position: relative;
}

.glassContainer p {
  color: #f8fafc;
  line-height: 1.6;
  font-size: 0.95rem;
  z-index: 10;
  position: relative;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

/* ====== HOMEPAGE SECTIONS STYLING ====== */
.whatIsArchon, .quickStart, .nextSteps, .callToAction {
  padding: 4rem 0;
}

.whatIsArchon {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

.whatIsArchon h2 {
  color: #ffffff;
  text-shadow: 0 0 20px rgba(168, 85, 247, 0.6);
  text-align: center;
  margin-bottom: 2rem;
}

.whatIsArchon p {
  color: #e5e7eb;
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.whatIsArchon a {
  color: #a855f7;
  text-decoration: none;
  border-bottom: 1px solid rgba(168, 85, 247, 0.3);
  transition: all 0.2s ease;
}

.whatIsArchon a:hover {
  color: #c084fc;
  border-bottom-color: #c084fc;
  text-shadow: 0 0 10px rgba(192, 132, 252, 0.5);
}

.features {
  padding: 5rem 0;
}

.features h2 {
  color: #ffffff;
  text-shadow: 0 0 25px rgba(168, 85, 247, 0.8);
  font-size: 2.5rem;
  font-weight: 700;
}

.quickStart {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
}

.quickStart h2 {
  color: #ffffff;
  text-shadow: 0 0 20px rgba(168, 85, 247, 0.6);
  text-align: center;
  margin-bottom: 1.5rem;
}

.quickStart p {
  color: #e5e7eb;
  font-size: 1.1rem;
  text-align: center;
  margin-bottom: 2rem;
}

.nextSteps h2 {
  color: #ffffff;
  text-shadow: 0 0 20px rgba(168, 85, 247, 0.6);
  text-align: center;
  margin-bottom: 2rem;
}

.nextSteps ol {
  color: #e5e7eb;
  font-size: 1.05rem;
  line-height: 1.8;
}

.nextSteps ol li {
  margin-bottom: 0.75rem;
}

.nextSteps a {
  color: #a855f7;
  text-decoration: none;
  border-bottom: 1px solid rgba(168, 85, 247, 0.3);
  transition: all 0.2s ease;
}

.nextSteps a:hover {
  color: #c084fc;
  border-bottom-color: #c084fc;
  text-shadow: 0 0 10px rgba(192, 132, 252, 0.5);
}

.callToAction {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

.callToAction hr {
  border-color: rgba(168, 85, 247, 0.3);
  margin-bottom: 2rem;
}

.callToAction p {
  color: #e5e7eb;
  font-size: 1.2rem;
}

/* Enhanced SVGs with smaller size and stronger neon glow */
.featureSvg {
  height: 40px;
  width: 40px;
  filter: drop-shadow(0 0 10px rgba(168, 85, 247, 0.6));
  transition: all 0.3s ease;
  margin-bottom: 0.5rem;
}

.featureSvg:hover {
  filter: drop-shadow(0 0 25px rgba(168, 85, 247, 0.9));
  transform: scale(1.1);
}

/* Responsive design */
@media (max-width: 768px) {
  .hero__title {
    font-size: 2.5rem;
  }
  
  .hero__subtitle {
    font-size: 1.1rem;
  }

  .theme-doc-markdown .row .col > div,
  .markdown .row .col > div {
    padding: 0.625rem;
    min-height: 110px;
  }
  
  .theme-doc-markdown .row .col h3,
  .markdown .row .col h3 {
    font-size: 0.95rem;
    margin-bottom: 0.4rem;
  }
  
  .theme-doc-markdown .row .col p,
  .markdown .row .col p {
    font-size: 0.75rem;
    line-height: 1.3;
  }

  .glassContainer {
    padding: 1.25rem;
    margin-bottom: 1.5rem;
  }

  .glassContainer h3 {
    font-size: 1.1rem;
  }

  .glassContainer p {
    font-size: 0.9rem;
  }

  .features h2 {
    font-size: 2rem;
  }

  .whatIsArchon, .quickStart, .nextSteps, .callToAction {
    padding: 3rem 0;
  }

  .featureSvg {
    height: 35px;
    width: 35px;
  }
}

@media (max-width: 480px) {
  .hero__title {
    font-size: 2rem;
  }
  
  .hero__subtitle {
    font-size: 1rem;
  }

  .theme-doc-markdown .row .col > div,
  .markdown .row .col > div {
    padding: 0.5rem;
    min-height: 100px;
  }
  
  .theme-doc-markdown .row .col h3,
  .markdown .row .col h3 {
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
  }
  
  .theme-doc-markdown .row .col p,
  .markdown .row .col p {
    font-size: 0.7rem;
    line-height: 1.2;
  }

  .glassContainer {
    padding: 1rem;
    margin-bottom: 1.25rem;
  }

  .glassContainer h3 {
    font-size: 1rem;
  }

  .glassContainer p {
    font-size: 0.85rem;
  }

  .features h2 {
    font-size: 1.75rem;
  }

  .whatIsArchon, .quickStart, .nextSteps, .callToAction {
    padding: 2.5rem 0;
  }

  .featureSvg {
    height: 30px;
    width: 30px;
    margin-bottom: 0.4rem;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #333333;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a855f7;
}

/* Selection styling */
::selection {
  background: rgba(168, 85, 247, 0.3);
  color: #ffffff;
}

::-moz-selection {
  background: rgba(168, 85, 247, 0.3);
  color: #ffffff;
}

/* 
 * ===================================================================
 * IMPORTANT: ROUNDED CORNERS IN MERMAID DIAGRAMS
 * ===================================================================
 * 
 * CSS border-radius and SVG rx/ry attributes do NOT work reliably 
 * on dynamically generated Mermaid SVG elements.
 * 
 * To get rounded corners, use Mermaid's new shape syntax instead:
 * 
 * For rounded rectangles:
 *   A@{ shape: rounded, label: "Rounded Node" }
 *   OR
 *   A["Rounded Node"]@{ shape: rounded }
 * 
 * For stadium-shaped nodes (fully rounded):
 *   A@{ shape: stadium, label: "Stadium Node" }
 *   OR  
 *   A(Stadium Node)  // shorthand syntax
 * 
 * Other rounded shapes available in Mermaid v11.3.0+:
 *   - event (rounded rectangle)
 *   - terminal/pill (stadium)
 *   - rounded (rounded rectangle)
 * 
 * Example with multiple rounded shapes:
 *   flowchart TD
 *     A@{ shape: rounded, label: "Start Process" }
 *     B@{ shape: stadium, label: "User Input" }
 *     C@{ shape: event, label: "Event Triggered" }
 * 
 * ===================================================================
 */

/* Mermaid Diagram Aurora Glass Morphism Styling */
.mermaid {
  /* Dark glass morphism container with aurora theme */
  background: rgba(10, 10, 10, 0.8) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 8px !important; /* Subtle corners, not pills */
  border: 1px solid rgba(111, 85, 255, 0.4) !important;
  padding: 24px !important;
  margin: 24px 0 !important;
  position: relative !important;
  overflow: hidden !important;
  z-index: 1 !important;
  
  /* Hover effect */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Force override Mermaid's default yellow fills with higher specificity */
.mermaid svg g rect[fill="#ffd93d"],
.mermaid svg g rect[fill="rgb(255, 217, 61)"],
.mermaid svg g rect[fill="#ffff00"],
.mermaid svg g rect[fill="yellow"] {
  fill: rgba(17, 24, 39, 0.9) !important;
  stroke: #a855f7 !important;
  stroke-width: 2px !important;
}

.mermaid:hover {
  transform: translateY(-8px) !important;
  border-color: var(--aurora-cyan) !important;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 
              0 10px 10px -5px rgba(0, 0, 0, 0.04),
              0 0 30px rgba(54, 181, 239, 0.3) !important;
}

/* Top aurora gradient border */
.mermaid::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 1px !important;
  background: linear-gradient(90deg, 
    transparent, 
    var(--aurora-green) 20%, 
    var(--aurora-cyan) 40%,
    var(--aurora-pink) 60%, 
    var(--aurora-purple) 80%, 
    transparent
  ) !important;
  z-index: 2 !important;
}

/* SVG styling within Mermaid container */
.mermaid svg {
  background: transparent !important;
  border-radius: 12px !important;
  width: 100% !important;
  height: auto !important;
}

/* Text elements - Force override with highest specificity */
.mermaid svg text,
.mermaid svg g text,
.mermaid svg g g text,
.mermaid .nodeLabel,
.mermaid .edgeLabel,
.mermaid .cluster-label,
.mermaid svg tspan,
.mermaid svg g tspan {
  fill: #ffffff !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  text-anchor: middle !important;
}

/* Force override SVG font attribute */
.mermaid svg,
.mermaid svg g {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
}

/* Node styling - rounded corners must be set in Mermaid syntax, not CSS */
.mermaid .node rect,
.mermaid rect {
  fill: rgba(17, 24, 39, 0.9) !important;
  stroke: #a855f7 !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3)) !important;
}

.mermaid .node circle,
.mermaid .node ellipse,
.mermaid .node polygon {
  fill: rgba(17, 24, 39, 0.9) !important;
  stroke: #a855f7 !important;
  stroke-width: 2px !important;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3)) !important;
}

/* Special node types */
.mermaid .node.start rect,
.mermaid .node.end rect {
  fill: rgba(139, 92, 246, 0.8) !important;
  stroke: #ddd6fe !important;
}

.mermaid .node.process rect {
  fill: rgba(31, 41, 55, 0.9) !important;
  stroke: #c084fc !important;
}

.mermaid .node.decision polygon {
  fill: rgba(75, 85, 99, 0.8) !important;
  stroke: #a855f7 !important;
}

/* Edge/arrow styling */
.mermaid .edgePath path {
  stroke: #a855f7 !important;
  stroke-width: 2px !important;
  fill: none !important;
}

.mermaid .arrowheadPath {
  fill: #a855f7 !important;
  stroke: #a855f7 !important;
}

/* Cluster/subgraph styling */
.mermaid .cluster rect {
  fill: rgba(31, 41, 55, 0.6) !important;
  stroke: rgba(139, 92, 246, 0.8) !important;
  stroke-width: 1px !important;
  stroke-dasharray: 5,5 !important;
  rx: 8px !important;
}

/* Git diagram specific styling */
.mermaid .commit-main {
  fill: #8b5cf6 !important;
  stroke: #a855f7 !important;
}

.mermaid .commit-branch {
  fill: #c084fc !important;
  stroke: #ddd6fe !important;
}

/* Journey diagram styling */
.mermaid .section {
  fill: rgba(139, 92, 246, 0.3) !important;
}

.mermaid .task {
  fill: rgba(17, 24, 39, 0.9) !important;
  stroke: #a855f7 !important;
}

/* State diagram styling */
.mermaid .state-start circle,
.mermaid .state-end circle {
  fill: #8b5cf6 !important;
  stroke: #ddd6fe !important;
}

.mermaid .state rect {
  fill: rgba(17, 24, 39, 0.9) !important;
  stroke: #a855f7 !important;
  rx: 6px !important;
}

/* Timeline styling */
.mermaid .timeline-item {
  fill: rgba(17, 24, 39, 0.9) !important;
  stroke: #a855f7 !important;
}

/* XY Chart styling */
.mermaid .xychart .plot-background {
  fill: rgba(17, 24, 39, 0.5) !important;
}

.mermaid .xychart .grid line {
  stroke: rgba(139, 92, 246, 0.2) !important;
}

/* Sequence diagram styling */
.mermaid .actor {
  fill: rgba(17, 24, 39, 0.9) !important;
  stroke: #a855f7 !important;
}

.mermaid .messageLine0,
.mermaid .messageLine1 {
  stroke: #a855f7 !important;
  stroke-width: 2px !important;
}

.mermaid .labelBox {
  fill: rgba(17, 24, 39, 0.9) !important;
  stroke: #a855f7 !important;
}

/* Class diagram styling */
.mermaid .classBox {
  fill: rgba(17, 24, 39, 0.9) !important;
  stroke: #a855f7 !important;
}

.mermaid .divider {
  stroke: #6b7280 !important;
}

/* Ensure proper contrast and visibility */
.mermaid .label,
.mermaid .nodeLabel,
.mermaid .edgeLabel {
  color: #ffffff !important;
  fill: #ffffff !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-weight: 400 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8) !important;
}

/* Force font styling on all text elements */
/* Force rounded corners on SVG rectangles - using CSS rx/ry properties (SVG 2 spec) */
.mermaid svg rect {
  rx: 8px !important;
  ry: 8px !important;
  border-radius: 8px !important; /* Fallback for older browsers */
}

/* More specific targeting for flowchart nodes */
.mermaid g.nodes rect,
.mermaid .node rect,
.mermaid g[class*="node"] rect {
  rx: 8px !important;
  ry: 8px !important;
  border-radius: 8px !important;
}

.mermaid * {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

/* Specific styling for different text types */
.mermaid .cluster-label,
.mermaid .titleText {
  font-weight: 600 !important;
  font-size: 16px !important;
}

.mermaid .edgeLabel {
  font-size: 12px !important;
  font-weight: 400 !important;
}

/* Dark mode specific adjustments */
[data-theme='dark'] .mermaid {
  background: rgba(17, 24, 39, 0.9) !important;
  border-color: rgba(139, 92, 246, 0.4) !important;
}

[data-theme='dark'] .mermaid:hover {
  border-color: rgba(168, 85, 247, 0.6) !important;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .mermaid {
    padding: 16px !important;
    margin: 16px 0 !important;
    border-radius: 12px !important;
  }
  
  .mermaid svg {
    max-width: 100% !important;
    height: auto !important;
  }
}

/* Animation for diagram loading */
.mermaid {
  animation: mermaidFadeIn 0.6s ease-out !important;
}

@keyframes mermaidFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Prevent text selection on hover effects */
.mermaid svg text {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 
 * JavaScript fallback for browsers that don't support CSS rx/ry properties
 * This script will run after Mermaid renders and set SVG attributes directly
 */
.mermaid-rounded-fallback {
  /* This class will be added by JavaScript if CSS rx/ry isn't supported */
}

.mermaid-rounded-fallback rect[rx="0"],
.mermaid-rounded-fallback rect:not([rx]) {
  /* For browsers where CSS rx/ry doesn't work, JS will set attributes */
}

