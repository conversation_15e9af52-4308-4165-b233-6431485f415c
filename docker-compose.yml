# Docker Compose profiles:
# - Default (no profile): Starts archon-server, archon-mcp, and archon-frontend
# - Agents are opt-in: archon-agents starts only with the "agents" profile
# Usage:
#   docker compose up                        # Starts server, mcp, frontend (agents disabled)
#   docker compose --profile agents up -d    # Also starts archon-agents

services:
  # Server Service (FastAPI + Socket.IO + Crawling)
  archon-server:
    build:
      context: ./python
      dockerfile: Dockerfile.server
      args:
        BUILDKIT_INLINE_CACHE: 1
        ARCHON_SERVER_PORT: ${ARCHON_SERVER_PORT:-8181}
    container_name: archon-server
    ports:
      - "${ARCHON_SERVER_PORT:-8181}:${ARCHON_SERVER_PORT:-8181}"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - LOGFIRE_TOKEN=${LOGFIRE_TOKEN:-}
      - SERVICE_DISCOVERY_MODE=docker_compose
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ARCHON_SERVER_PORT=${ARCHON_SERVER_PORT:-8181}
      - ARCHON_MCP_PORT=${ARCHON_MCP_PORT:-8051}
      - ARCHON_AGENTS_PORT=${ARCHON_AGENTS_PORT:-8052}
      - AGENTS_ENABLED=${AGENTS_ENABLED:-false}
    networks:
      - app-network
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock # Docker socket for MCP container control
      - ./python/src:/app/src # Mount source code for hot reload
      - ./python/tests:/app/tests # Mount tests for UI test execution
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command:
      [
        "python",
        "-m",
        "uvicorn",
        "src.server.main:app",
        "--host",
        "0.0.0.0",
        "--port",
        "${ARCHON_SERVER_PORT:-8181}",
        "--reload",
      ]
    healthcheck:
      test:
        [
          "CMD",
          "sh",
          "-c",
          'python -c "import urllib.request; urllib.request.urlopen(''http://localhost:${ARCHON_SERVER_PORT:-8181}/health'')"',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Lightweight MCP Server Service (HTTP-based)
  archon-mcp:
    build:
      context: ./python
      dockerfile: Dockerfile.mcp
      args:
        BUILDKIT_INLINE_CACHE: 1
        ARCHON_MCP_PORT: ${ARCHON_MCP_PORT:-8051}
    container_name: archon-mcp
    ports:
      - "${ARCHON_MCP_PORT:-8051}:${ARCHON_MCP_PORT:-8051}"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - LOGFIRE_TOKEN=${LOGFIRE_TOKEN:-}
      - SERVICE_DISCOVERY_MODE=docker_compose
      - TRANSPORT=sse
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      # MCP needs to know where to find other services
      - API_SERVICE_URL=http://archon-server:${ARCHON_SERVER_PORT:-8181}
      - AGENTS_ENABLED=${AGENTS_ENABLED:-false}
      - AGENTS_SERVICE_URL=${AGENTS_SERVICE_URL:-http://archon-agents:${ARCHON_AGENTS_PORT:-8052}}
      - ARCHON_MCP_PORT=${ARCHON_MCP_PORT:-8051}
      - ARCHON_SERVER_PORT=${ARCHON_SERVER_PORT:-8181}
      - ARCHON_AGENTS_PORT=${ARCHON_AGENTS_PORT:-8052}
    networks:
      - app-network
    depends_on:
      archon-server:
        condition: service_healthy
      
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test:
        [
          "CMD",
          "sh",
          "-c",
          'python -c "import socket; s=socket.socket(); s.connect((''localhost'', ${ARCHON_MCP_PORT:-8051})); s.close()"',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s # Give dependencies time to start

  # AI Agents Service (ML/Reranking)
  archon-agents:
    profiles:
      - agents  # Only starts when explicitly using --profile agents
    build:
      context: ./python
      dockerfile: Dockerfile.agents
      args:
        BUILDKIT_INLINE_CACHE: 1
        ARCHON_AGENTS_PORT: ${ARCHON_AGENTS_PORT:-8052}
    container_name: archon-agents
    ports:
      - "${ARCHON_AGENTS_PORT:-8052}:${ARCHON_AGENTS_PORT:-8052}"
    environment:
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_SERVICE_KEY=${SUPABASE_SERVICE_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - LOGFIRE_TOKEN=${LOGFIRE_TOKEN:-}
      - SERVICE_DISCOVERY_MODE=docker_compose
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ARCHON_AGENTS_PORT=${ARCHON_AGENTS_PORT:-8052}
      - ARCHON_SERVER_PORT=${ARCHON_SERVER_PORT:-8181}
    networks:
      - app-network
    healthcheck:
      test:
        [
          "CMD",
          "sh",
          "-c",
          'python -c "import urllib.request; urllib.request.urlopen(''http://localhost:${ARCHON_AGENTS_PORT:-8052}/health'')"',
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Frontend
  archon-frontend:
    build: ./archon-ui-main
    container_name: archon-ui
    ports:
      - "${ARCHON_UI_PORT:-3737}:3737"
    environment:
      - VITE_API_URL=http://${HOST:-localhost}:${ARCHON_SERVER_PORT:-8181}
      - VITE_ARCHON_SERVER_PORT=${ARCHON_SERVER_PORT:-8181}
      - ARCHON_SERVER_PORT=${ARCHON_SERVER_PORT:-8181}
      - HOST=${HOST:-localhost}
      - PROD=${PROD:-false}
      - VITE_ALLOWED_HOSTS=${VITE_ALLOWED_HOSTS:-}
      - VITE_SHOW_DEVTOOLS=${VITE_SHOW_DEVTOOLS:-false}
    networks:
      - app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3737"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - ./archon-ui-main/src:/app/src
      - ./archon-ui-main/public:/app/public
    depends_on:
      archon-server:
        condition: service_healthy

networks:
  app-network:
    driver: bridge
