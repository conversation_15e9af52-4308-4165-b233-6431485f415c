# Minimal startup configuration - only Supabase connection required
# All other settings (API keys, model choices, RAG flags) are managed via the Settings page

# Get your SUPABASE_URL from the Data API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# ⚠️ CRITICAL: You MUST use the SERVICE ROLE key, NOT the Anon key! ⚠️
# 
# COMMON MISTAKE: Using the anon (public) key will cause ALL saves to fail with "permission denied"!
#
# How to get the CORRECT key:
# 1. Go to: https://supabase.com/dashboard/project/<your project ID>/settings/api
# 2. In the Settings menu, click on "API keys" 
# 3. Find "Project API keys" section
# 4. You will see TWO keys - choose carefully:
#    ❌ anon (public): WRONG - This is shorter, starts with "eyJhbGc..." and contains "anon" in the JWT
#    ✅ service_role (secret): CORRECT - This is longer and contains "service_role" in the JWT
#
# The service_role key is typically much longer than the anon key.
# If you see errors like "Failed to save" or "Permission denied", you're using the wrong key!
#
# On the Supabase dashboard, it's labeled as "service_role" under "Project API keys"
SUPABASE_SERVICE_KEY=

# Optional: Set log level for debugging
LOGFIRE_TOKEN=
LOG_LEVEL=INFO

# Service Ports Configuration
# These ports are used for external access to the services
HOST=localhost
ARCHON_SERVER_PORT=8181
ARCHON_MCP_PORT=8051
ARCHON_AGENTS_PORT=8052
ARCHON_UI_PORT=3737
ARCHON_DOCS_PORT=3838

# Frontend Configuration
# VITE_ALLOWED_HOSTS: Comma-separated list of additional hosts allowed for Vite dev server
# Example: VITE_ALLOWED_HOSTS=*************,myhost.local,example.com
# If not set, defaults to localhost, 127.0.0.1, ::1, and the HOST value above
VITE_ALLOWED_HOSTS=

# Development Tools
# VITE_SHOW_DEVTOOLS: Show TanStack Query DevTools (for developers only)
# Set to "true" to enable the DevTools panel in bottom right corner
# Defaults to "false" for end users
VITE_SHOW_DEVTOOLS=false

# When enabled, PROD mode will proxy ARCHON_SERVER_PORT through ARCHON_UI_PORT. This exposes both the 
# Archon UI and API through a single port. This is useful when deploying Archon behind a reverse 
# proxy where you want to expose the frontend on a single external domain.
PROD=false

# Embedding Configuration
# Dimensions for embedding vectors (1536 for OpenAI text-embedding-3-small)
EMBEDDING_DIMENSIONS=1536

# NOTE: All other configuration has been moved to database management!
# Run the credentials_setup.sql file in your Supabase SQL editor to set up the credentials table.
# Then use the Settings page in the web UI to manage:
# - OPENAI_API_KEY (encrypted)
# - MODEL_CHOICE 
# - TRANSPORT settings
# - RAG strategy flags (USE_CONTEXTUAL_EMBEDDINGS, USE_HYBRID_SEARCH, etc.)
# - Crawler settings:
#   * CRAWL_MAX_CONCURRENT (default: 10) - Max concurrent pages per crawl operation
#   * CRAWL_BATCH_SIZE (default: 50) - URLs processed per batch
#   * MEMORY_THRESHOLD_PERCENT (default: 80) - Memory % before throttling
#   * DISPATCHER_CHECK_INTERVAL (default: 0.5) - Memory check interval in seconds
